trigger:
  branches:
    include:
      - dev
      - staging
      - main

parameters:
- name: deployments
  type: object
  default:
    - namespace: people-product-dev
      serviceConnection: peopleproducts-aks-gb-dev-people-product-dev
    - namespace: lca-dev
      serviceConnection: people-products-aks-gb-dev-lca-dev
    
resources:
  repositories:
    - repository: appsectemplates
      type: git
      name: DevSecOps/DevSecOps

variables:
  - ${{ if eq(variables['Build.Reason'], 'Manual') }}:
      - group: "peopleplatform-deploy-development"
      - group: "build-resources"
  - ${{ if eq(variables['Build.SourceBranch'], 'refs/heads/dev') }}:
      - group: "peopleplatform-deploy-development"
      - group: "build-resources"
  - ${{ if eq(variables['Build.SourceBranch'], 'refs/heads/staging') }}:
      - group: "peopleplatform-deploy-staging"
      - group: "build-resources"
  - ${{ if eq(variables['Build.SourceBranch'], 'refs/heads/main') }}:
      - group: "peopleplatform-deploy-production"
      - group: "build-resources"

  - name: scanTemplate
    ${{ if eq(variables['Build.Reason'], 'PullRequest') }}:
      value: 'pipeline_templates/Security_tasks/prepareSonarcloudPR.yml@appsectemplates'
    ${{ else }}:
      value: 'pipeline_templates/Security_tasks/prepareSonarCloud.yml@appsectemplates'

stages:
- stage: Build
  displayName: Build and secure code scan
  jobs:
    - job: Build
      displayName: Build
      pool: 
        vmImage: $(vmImageName)

      steps:
        - checkout: self
        - checkout: appsectemplates

        # install dependecies
        - task: NodeTool@0
          inputs:
            versionSource: "spec"
            versionSpec: "18.x"

        #NPM Setup and Build
        - task: Bash@3
          displayName: 'Create .npmrc file'
          inputs:
            targetType: 'inline'
            script: |
              echo "legacy-peer-deps=true" > $(Build.SourcesDirectory)/$(Build.Repository.Name)/.npmrc
              echo "registry=https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/" >> $(Build.SourcesDirectory)/$(Build.Repository.Name)/.npmrc
              echo "always-auth=true" >> $(Build.SourcesDirectory)/$(Build.Repository.Name)/.npmrc

        - task: npmAuthenticate@0
          displayName: 'Authenticate NPM'
          inputs:
            workingFile: '$(Build.SourcesDirectory)/$(Build.Repository.Name)/.npmrc'

        - script: |
            npm ci --legacy-peer-deps
          displayName: 'NPM install'
          workingDirectory: '$(Build.SourcesDirectory)/$(Build.Repository.Name)'
           

        - template: ${{ variables['scanTemplate'] }}
          parameters:
            SCServiceConnection: "SonarcloudServer"
            SCProjectKey: 'GHQ_ABI_PEOPLE_TECH_PLATFORM_GHQ_ABI_PEOPLE_TECH_PLATFORM_AUTH_API'
            SCProjectName: 'GHQ_ABI_PEOPLE_TECH_PLATFORM_GHQ_ABI_PEOPLE_TECH_PLATFORM_AUTH_API'
            SCBaseDirPath: "./$(Build.Repository.Name)"
            SCSourceEncoding: "UTF-8"
            SCReportsPathType: sonar.javascript.lcov.reportPaths
            SCReportsPath: ./coverage/lcov.info
            ${{ if or(eq(variables['Build.Reason'], 'IndividualCI'), eq(variables['Build.Reason'], 'Manual'))}}:
              SCBranchName: "$(Build.SourceBranchName)"
            ${{ if eq(variables['Build.Reason'], 'PullRequest') }}:
              SCPRKey: $(System.PullRequest.PullRequestId)
              SCPrBranch: $(System.PullRequest.SourceBranch)
              SCPrBaseBranch: $(System.PullRequest.TargetBranch)
                    
        - script: |
            npm run coverage
          displayName: 'NPM run coverage'
          workingDirectory: $(Build.SourcesDirectory)/$(Build.Repository.Name)
        
        # - task: PublishCodeCoverageResults@2
        #   inputs:
        #     codeCoverageTool: Cobertura
        #     summaryFileLocation: $(Build.SourcesDirectory)/$(Build.Repository.Name)/coverage/cobertura-coverage.xml

        - task: Docker@2
          displayName: 'Docker build and push'
          inputs:
              containerRegistry: 'AzurePeopleProducts'
              repository: 'auth-api'
              command: 'buildAndPush'
              Dockerfile: '**/Dockerfile'
              tags: |
                $(tag)
                latest
              # buildContext: '.'

        - task: AzureCLI@2
          displayName: 'Sign and Verify Image'
          inputs:
              azureSubscription: 'AzureNonProd'
              scriptType: 'bash'
              scriptLocation: 'inlineScript'
              inlineScript: |
                curl -sL https://github.com/sigstore/cosign/releases/latest/download/cosign-linux-amd64 --output /usr/local/bin/cosign
                chmod +x /usr/local/bin/cosign
                
                az acr login -n $(containerRegistry)
                IMAGE_DIGEST=$(az acr repository show --name $(containerRegistry) --image auth-api:$(tag) --query digest --output tsv)
              
                cosign sign --key azurekms://$(AKV_URI)/$(SigningKeyName) \
                  --tlog-upload=false \
                  $(containerRegistry)/auth-api@$IMAGE_DIGEST
                
                cosign verify --key azurekms://$(AKV_URI)/$(SigningKeyName) \
                  --insecure-ignore-tlog \
                  $(containerRegistry)/auth-api:$(tag)
          env:
            AZURE_TENANT_ID: cef04b19-7776-4a94-b89b-375c77a8f936
            AZURE_CLIENT_ID: $(appsec-akv-sp-clientid)
            AZURE_CLIENT_SECRET: $(appsec-akv-sp-password)
                  
        - template: pipeline_templates/secure_code_scan.yml@appsectemplates
          parameters:
            scanSonarCloud: true
            sonarCloudGate: false        ##keep true before prod push and resolve sonar issues
            SCServiceConnection: "SonarcloudServer"
            SCOrganization: "sonarcloud-ado"
        
            scanSnyk: false
            scanSnykTarget: true
            SkTargetFile: '$(Build.Repository.Name)/package.json'
            SKFailOnIssues: false          ##keep true before prod push and resolve snyk issues
            SkProjectName: '$(Build.SourcesDirectory)'
            SkServiceConnection: "SnykServer"
            SkOrganization: "************************************"
            App360ID: "SE-04933"

            scanApiiro: true
            AprServiceConnection: "apiiro"
            AprSkipOnScanFailure: true       ##keep false before prod push and resolve apiiro issues
        
        - task: PublishPipelineArtifact@1
          displayName: 'Publish Manifests'
          inputs:
            artifactName: 'manifests'
            targetPath: $(Build.SourcesDirectory)

################################### Stage Deployment ###############################
- stage: StagingDeployment
  displayName: 'Deploy to Staging Environment'
  dependsOn: Build
  condition: and(succeeded(), eq(variables['Build.SourceBranch'], 'refs/heads/staging'))
  jobs:

      - job: Deploy
        displayName: Deploy
        pool:
          vmImage: $(vmImageName)
        steps:
          - task: KubernetesManifest@1
            inputs:
              action: 'deploy'
              connectionType: 'kubernetesServiceConnection'
              kubernetesServiceConnection: 'peopleproducts-aks-gb-dev-people-product-stg'
              namespace: 'people-product-stg'
              manifests: |
                 pipelines/staging/manifests/*.yaml
                 pipelines/staging/manifests/*.yml
              containers: '$(containerRegistry)/auth-api:$(tag)'

          - task: Kubernetes@1
            displayName: image update
            inputs:
              connectionType: 'Kubernetes Service Connection'
              kubernetesServiceEndpoint: 'peopleproducts-aks-gb-dev-people-product-stg'
              namespace: 'people-product-stg'
              command: 'set'
              arguments: 'image deployment/auth-api auth-api=peopleproductsacr.azurecr.io/auth-api:$(Build.BuildId)'
              secretType: 'dockerRegistry'
              containerRegistryType: 'Azure Container Registry'

####################### Dev Deployment #########################################
- stage: DevelopmentDeployment
  displayName: 'Deploy to Development Environment'
  dependsOn: Build
  condition: and(succeeded(), or(eq(variables['Build.SourceBranch'], 'refs/heads/dev'), eq(variables['Build.Reason'], 'Manual')))
  jobs:
  
  - job: Deploy
    displayName: Deploy
    pool:
      vmImage: $(vmImageName)

    steps:
      - ${{ each deploy in parameters.deployments }}:
      # Deploy manifest files based on namespace
        - task: KubernetesManifest@1
          displayName: 'Deploy to ${{ deploy.namespace }}'
          condition: eq('${{ deploy.namespace }}', 'lca-dev')
          inputs:
            action: deploy
            connectionType: 'kubernetesServiceConnection'
            kubernetesServiceConnection: ${{ deploy.serviceConnection }}
            namespace: ${{ deploy.namespace }}
            manifests: |
              pipelines/development/manifests/deployment-lca.yaml
              pipelines/development/manifests/service.yaml
              pipelines/development/manifests/ingress.yaml
              pipelines/development/manifests/secrets.yaml
            containers: |
              peopleproductsacr.azurecr.io/auth-api:$(Build.BuildId)
              peopleproductsacr.azurecr.io/auth-api:latest

        - task: KubernetesManifest@1
          displayName: 'Deploy to ${{ deploy.namespace }}'
          condition: ne('${{ deploy.namespace }}', 'lca-dev')
          inputs:
            action: deploy
            connectionType: 'kubernetesServiceConnection'
            kubernetesServiceConnection: ${{ deploy.serviceConnection }}
            namespace: ${{ deploy.namespace }}
            manifests: |
              pipelines/development/manifests/deployment.yaml
              pipelines/development/manifests/service.yaml
              pipelines/development/manifests/ingress.yaml
              pipelines/development/manifests/secrets.yaml
             
            containers: |
              peopleproductsacr.azurecr.io/auth-api:$(Build.BuildId)
              peopleproductsacr.azurecr.io/auth-api:latest
              
        - task: Kubernetes@1
          displayName: 'Update image in ${{ deploy.namespace }}'
          inputs:
            connectionType: 'Kubernetes Service Connection'
            kubernetesServiceEndpoint: ${{ deploy.serviceConnection }}
            namespace: ${{ deploy.namespace }}
            command: 'set'
            arguments: 'image deployment/auth-api auth-api=peopleproductsacr.azurecr.io/auth-api:$(Build.BuildId)'
            secretType: 'dockerRegistry'
            containerRegistryType: 'Azure Container Registry'

# ###################### Prod Deployment ################################################################
- stage: ProductionDeployment
  displayName: 'Deploy to Production Environment'
  dependsOn: Build
  condition: and(succeeded(), eq(variables['Build.SourceBranch'], 'refs/heads/main'))
  jobs:

  - job: Deploy
    displayName: Deploy 
    pool: 
     vmImage: $(vmImageName)         
    steps:
      - task: KubernetesManifest@1
        displayName: Deploy
        inputs:
            action: deploy
            connectionType: 'kubernetesServiceConnection'
            kubernetesServiceConnection: 'peopleproducts-aks-gb-dev-people-product-prod'
            namespace: 'people-product-prod'
            manifests: |
               pipelines/production/manifests/*.yml
            containers: |
                $(containerRegistry)/auth-api:$(tag)
      - task: Kubernetes@1
        displayName: image update
        inputs:
            connectionType: 'Kubernetes Service Connection'
            kubernetesServiceEndpoint: 'peopleproducts-aks-gb-dev-people-product-prod'
            namespace: 'people-product-prod'
            command: 'set'
            arguments: 'image deployment/auth-api auth-api=peopleproductsacr.azurecr.io/auth-api:$(Build.BuildId)'
            secretType: 'dockerRegistry'
            containerRegistryType: 'Azure Container Registry'