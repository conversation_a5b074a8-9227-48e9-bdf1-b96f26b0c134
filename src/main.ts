import { NestFactory } from '@nestjs/core';
import { AppModule } from './app/app.module';
import { ValidationPipe } from '@nestjs/common';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import { tracer } from 'dd-trace';

process.env.DD_ENV &&
  tracer.init({
    env: process.env.DD_ENV,
    service: 'auth-api',
    logInjection: true,
    runtimeMetrics: true,
    logLevel: 'debug',
    tags: {
      'service': 'auth-api',
      'version': process.env.npm_package_version,
      'env': process.env.DD_ENV,
    }
  });

async function bootstrap() {
  const app = await NestFactory.create(AppModule);
  const GLOBAL_PREFIX = process.env.GLOBAL_PREFIX || '';

  app.setGlobalPrefix(GLOBAL_PREFIX);
  app.useGlobalPipes(new ValidationPipe());

  app.enableCors({
    credentials: true,
    preflightContinue: false,
    optionsSuccessStatus: 204,
    origin: [process.env.CORS_ORIGIN, /\.ab-inbev.com$/],
    methods: ['GET', 'HEAD', 'PUT', 'PATCH', 'POST', 'DELETE']
  });

  if (process.env.NODE_ENV == 'development') {
    const config = new DocumentBuilder()
      .setTitle('People Platform Auth')
      .setDescription('Auth APIS')
      .addBearerAuth()
      .setVersion('1.0')
      .build();

    const document = SwaggerModule.createDocument(app, config);
    SwaggerModule.setup(GLOBAL_PREFIX + '/swagger-ui', app, document);
  }

  await app.listen(process.env.SERVER_PORT || 3000, () => {
    console.info(`Application started on port: ${process.env.SERVER_PORT || 3000}`);
  });
}
bootstrap();
