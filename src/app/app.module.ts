import { JwtAuthGuard } from '@/@core/infra/jwt/jwt-auth.guard';
import { JwtStrategy } from '@/@core/infra/jwt/jwt.strategy';
import { AuthV2Module } from '@/auth/auth-v2.module';
import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { APP_GUARD } from '@nestjs/core';
import { JwtService } from '@nestjs/jwt';
import { TypeOrmModule } from '@nestjs/typeorm';
import { typeOrmAsyncConfig } from 'src/@core/infra/db/typeorm/config/config';
import { HealthCheckModule } from 'src/health-check/health-check.modules';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      ignoreEnvFile: false,
      envFilePath: ['.env', '.env.development', '.env.production']
    }),
    TypeOrmModule.forRootAsync(typeOrmAsyncConfig),
    HealthCheckModule,
    AuthV2Module
  ],
  providers: [
    {
      provide: JwtStrategy,
      useFactory: () => new JwtStrategy()
    },
    {
      provide: APP_GUARD,
      useClass: JwtAuthGuard
    },
    {
      provide: JwtService,
      useValue: new JwtService({ secret: process.env.JWT_REFRESH_SECRET })
    }
  ]
})
export class AppModule {}
