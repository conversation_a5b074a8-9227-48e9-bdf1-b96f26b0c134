import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsOptional, IsString, ValidateIf } from 'class-validator';

export class AuthenticateEmployeeDto {
  @ApiProperty()
  @ValidateIf((o) => !o.upn_email && !o.ad_email)
  @IsNotEmpty()
  global_id: string;

  @ApiProperty()
  @ValidateIf((o) => !o.global_id && !o.ad_email)
  @IsNotEmpty()
  upn_email: string;

  @ApiProperty()
  @ValidateIf((o) => !o.global_id && !o.upn_email)
  @IsNotEmpty()
  ad_email: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  proxy_user_id?: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  proxy_employee_global_id?: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  requester_app_key?: string;
}
