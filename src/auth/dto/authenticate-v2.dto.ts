import { ApiProperty } from '@nestjs/swagger';
import { SystemEnum } from 'abi-opr-common-types';
import { IsEnum, IsNotEmpty } from 'class-validator';

export class AuthenticateEmployeeV2Dto {
  @ApiProperty({ required: true, description: 'azure token', type: 'string' })
  @IsNotEmpty()
  azToken: string;

  @IsEnum(SystemEnum)
  @ApiProperty({ required: true, description: 'system parameter', type: 'string', enum: SystemEnum })
  @IsNotEmpty()
  system: SystemEnum;
}
