import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { AuthenticateEmployeeUseCase } from 'src/@core/application/use-cases/authenticateEmployee/Authenticate.use-case';
import { Employee } from 'src/@core/domain/employee/Employee.entity';
import { AzureAuthValidatorService } from '@/@core/infra/azure/azure-auth-validator.service';
import { DecodedJwtToken2, SystemEnum } from 'abi-opr-common-types';
import { AuthenticateEmployeeDto } from './dto/authenticate.dto';
import { AuthenticateEmployeeV2Dto } from './dto/authenticate-v2.dto';
import { CreateSimplifiedTokenUseCase } from '@/@core/application/use-cases/jwt/CreateSimplifiedToken.use-case';
import RefreshSimplifiedTokenUseCase from '@/@core/application/use-cases/jwt/RefreshSimplifiedToken.use-case';
import { ProxyDto } from './dto/proxy.dto';
import GetEmployeeByIdUseCase from '@/@core/application/use-cases/employee/GetEmployeeById.use-case';
import GetEmployeeByGlobalUseCase from '@/@core/application/use-cases/employee/GetEmployeeByGlobalId.use-case';
import { EmployeeAdoptionUseCase } from '@/@core/application/use-cases/employeeAdoption/EmployeeAdoption.use-case';

@Injectable()
export class AuthV2Service {
  logger: Logger;

  constructor(
    private readonly authenticateEmployeeUseCase: AuthenticateEmployeeUseCase,
    private readonly azureAuthValidatorService: AzureAuthValidatorService,
    private readonly createSimplifiedTokenUseCase: CreateSimplifiedTokenUseCase,
    private readonly refreshSimplifiedTokenUseCase: RefreshSimplifiedTokenUseCase,
    private readonly getEmployeeByIdUseCase: GetEmployeeByIdUseCase,
    private readonly getEmployeeByGlobalUseCase: GetEmployeeByGlobalUseCase,
    private readonly employeeAdoptionUseCase: EmployeeAdoptionUseCase
  ) {
    this.logger = new Logger(AuthV2Service.name);
  }

  async authenticateEmployeeByAzureToken(authDto: AuthenticateEmployeeV2Dto) {
    const tokenValidate = await this.azureAuthValidatorService.authenticate(authDto.azToken);

    this.logger.log(
      `Azure AD auth result: ad_email: '${tokenValidate.email}', upn_email: '${tokenValidate.upn}', global_id: '${tokenValidate.abiGlobalID}'`
    );

    return await this.authenticateEmployee(
      {
        ad_email: tokenValidate.email,
        upn_email: tokenValidate.upn,
        global_id: tokenValidate?.abiGlobalID
      } as AuthenticateEmployeeDto,
      authDto.system
    );
  }

  async refreshToken(refreshToken: string): Promise<[Employee, string, string]> {
    const [employee, behalfOf, system] = await this.refreshSimplifiedTokenUseCase.execute(refreshToken);

    if (!employee) {
      this.logger.error('User not found authToken ' + refreshToken);
      throw new NotFoundException('User not found');
    }

    const [token, refresh] = await this.getTokens(employee, system, behalfOf);

    await this.saveAdoption(employee, system);

    return [employee, token, refresh];
  }

  async authenticateProxyUser(proxyDto: ProxyDto, decodedToken: DecodedJwtToken2): Promise<[Employee, string, string]> {
    const [currentEmployee, proxyEmployee] = await Promise.all([
      this.getEmployeeByIdUseCase.execute(decodedToken.uid),
      this.getEmployeeByGlobalUseCase.execute(proxyDto.proxyEmployeeGlobalId)
    ]);

    const [token, refreshToken] = await this.getTokens(proxyEmployee, decodedToken.system, currentEmployee.uid);

    return [proxyEmployee, token, refreshToken];
  }

  private async authenticateEmployee(authData: AuthenticateEmployeeDto, system: SystemEnum): Promise<[Employee, string, string]> {
    const employee = await this.authenticateEmployeeUseCase.execute(authData);

    if (!employee) {
      this.logger.error('User not found authData ' + JSON.stringify(authData));
      throw new NotFoundException('User not found');
    }

    const [newToken, newRefreshToken] = await this.getTokens(employee, system, employee.uid);

    const employeeLog = { ...employee, employee_band: undefined, employee_band_group: undefined };

    this.logger.log(`Employee authenticated ${JSON.stringify(employeeLog)} - System ${system}}`);

    await this.saveAdoption(employee, system);

    return [employee, newToken, newRefreshToken];
  }

  private async getTokens(employee: Employee, system: SystemEnum, behalfOf: string): Promise<[string, string]> {
    const [token, refreshToken] = await this.createSimplifiedTokenUseCase.execute(employee, system, behalfOf);
    return [token, refreshToken];
  }

  private async saveAdoption(employee: Employee, system: SystemEnum): Promise<void> {
    try {
      await this.employeeAdoptionUseCase.execute(employee, system);
    } catch (error) {
      this.logger.error(`Error while saving employee adoption: ${error}`);
    }
  }
}
