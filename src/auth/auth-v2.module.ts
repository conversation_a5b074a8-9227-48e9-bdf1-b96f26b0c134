import { AzureModule } from '@/@core/infra/azure/azure.module';
import { Module } from '@nestjs/common';
import { JwtModule } from '@nestjs/jwt';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AuthV2Controller } from './auth-v2.controller';
import { Employee } from '@/@core/domain/employee/Employee.entity';
import { EmployeeRepositoryKey } from '@/@core/domain/employee/IEmployeeRepository';
import { EmployeeTypeOrmRepository } from '@/@core/infra/db/typeorm/repositories/employees/employee.repository';
import { AuthenticateEmployeeUseCase } from '@/@core/application/use-cases/authenticateEmployee/Authenticate.use-case';
import { AuthV2Service } from './auth-v2.service';
import { CreateSimplifiedTokenUseCase } from '@/@core/application/use-cases/jwt/CreateSimplifiedToken.use-case';
import RefreshSimplifiedTokenUseCase from '@/@core/application/use-cases/jwt/RefreshSimplifiedToken.use-case';
import { ProxyRulesUseCase } from '@/@core/application/use-cases/proxyRules/ProxyRules.use-case';
import GetEmployeeByIdUseCase from '@/@core/application/use-cases/employee/GetEmployeeById.use-case';
import GetEmployeeByGlobalUseCase from '@/@core/application/use-cases/employee/GetEmployeeByGlobalId.use-case';
import { PeopleApiModule } from '@/@core/infra/people-api-gateway/people-api.module';
import { EmployeeAdoptionUseCase } from '@/@core/application/use-cases/employeeAdoption/EmployeeAdoption.use-case';

@Module({
  imports: [
    TypeOrmModule.forFeature([Employee]),
    JwtModule.register({
      secret: process.env.JWT_SECRET,
      signOptions: { expiresIn: process.env.JWT_EXP || '15m' }
    }),
    AzureModule,
    PeopleApiModule
  ],
  controllers: [AuthV2Controller],
  providers: [
    AuthV2Service,
    CreateSimplifiedTokenUseCase,
    AuthenticateEmployeeUseCase,
    RefreshSimplifiedTokenUseCase,
    ProxyRulesUseCase,
    GetEmployeeByIdUseCase,
    GetEmployeeByGlobalUseCase,
    EmployeeAdoptionUseCase,
    {
      provide: EmployeeRepositoryKey,
      useClass: EmployeeTypeOrmRepository
    }
  ]
})
export class AuthV2Module {}
