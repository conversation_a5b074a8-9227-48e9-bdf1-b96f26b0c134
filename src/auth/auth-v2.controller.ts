import { Body, Controller, Get, Logger, <PERSON>, <PERSON>s, Headers, UseInterceptors } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { Response } from 'express';
import { Public } from '@/@core/application/decorators/public.decorator';
import { AuthenticateEmployeeV2Dto } from './dto/authenticate-v2.dto';
import { AuthV2Service } from './auth-v2.service';
import { ProxyInterceptor } from '@/@core/infra/interceptors/proxy-interceptor';
import { ProxyDto } from './dto/proxy.dto';
import { DecodedToken } from '@/@core/application/decorators/token-decoded.decorator';
import { DecodedJwtToken2 } from 'abi-opr-common-types';

@ApiTags('Auth V2')
@Controller('v2/auth')
export class AuthV2Controller {
  private logger = new Logger(AuthV2Controller.name);

  constructor(private authV2Service: AuthV2Service) {}

  @Post('/')
  @Public()
  async authenticate(@Body() authData: AuthenticateEmployeeV2Dto, @Res() response: Response) {
    const [employee, token, refreshToken] = await this.authV2Service.authenticateEmployeeByAzureToken(authData);
    this.logger.log(`AuthLoginV2 - email: '${employee.email}' - Logged as '${employee.uid}'`);
    return response.status(200).json({ employee, token, refreshToken });
  }

  @Get('refresh')
  async refreshToken(@Headers() headers: Record<string, string>, @Res() response: Response) {
    const oldSecretToken = headers['authorization'];

    const [employee, token, refreshToken] = await this.authV2Service.refreshToken(oldSecretToken);

    this.logger.log(`Refresh - email: '${employee.email}' - Logged as '${employee.uid}'`);
    return response.status(200).json({ employee, token, refreshToken });
  }

  @UseInterceptors(ProxyInterceptor)
  @Post('/proxy')
  async authenticateProxy(@Body() authData: ProxyDto, @DecodedToken() decodedToken: DecodedJwtToken2, @Res() response: Response) {
    const [employee, token, refreshToken] = await this.authV2Service.authenticateProxyUser(authData, decodedToken);
    this.logger.log(`User '${decodedToken.uid}' proxyng for user '${employee.uid}' `);
    return response.status(200).json({ employee, token, refreshToken });
  }
}
