import { Controller, Get } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { HealthCheckService } from './health-check.service';
import { Public } from '@/@core/application/decorators/public.decorator';

@ApiTags('HealthCheck')
@Controller('health-check')
export class HealthCheckController {
  constructor(private healthCheck: HealthCheckService) {}

  @Get()
  @Public()
  async simpleHealthCheck() {
    return this.healthCheck.getOk();
  }
}
