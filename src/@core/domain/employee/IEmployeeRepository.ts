import { ListEmployeeFiltersDTO } from 'src/@core/application/dto/listEmployeeFiltersDTO';
import { Employee } from './Employee.entity';

export const EmployeeRepositoryKey = 'EmployeeRepositoryKey';
export interface IEmployeeRepository {
  findByEmail(email: string): Promise<Employee>;
  findByGlobalId(id: string): Promise<Employee>;
  findByIdOrEmails(id: string, upn_email, ad_email: string): Promise<Employee>;
  findByUid(uid: string): Promise<Employee>;
  findAll(filters?: ListEmployeeFiltersDTO): Promise<any>;
  isEmployeeManager(employeeId: string): Promise<boolean>;
}
