import { ApiProperty } from '@nestjs/swagger';
import {
  Column,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  JoinColumn,
  ManyToOne,
  PrimaryColumn,
  UpdateDateColumn
} from 'typeorm';
import { partnerStatusEnum } from './EnumPartnerStatus.enum';

export enum EmployeeStatusEnum {
  ACTIVE = 'Active',
  INACTIVE = 'Inactive',
  TERMINATED = 'Terminated',
  RETIRED = 'Retired',
  WITHDRAWN = 'Withdrawn'
}

@Entity({ name: 'employee_wd' })
export class Employee {
  @CreateDateColumn({
    name: 'created_at'
  })
  createdAt: Date;

  @UpdateDateColumn({
    name: 'updated_at'
  })
  updatedAt: Date;

  @DeleteDateColumn({
    name: 'deleted_at',
    nullable: true
  })
  deletedAt?: Date;

  @ApiProperty({ required: false })
  @PrimaryColumn({ name: 'id' })
  uid: string;

  @ApiProperty({ required: false })
  @Column({ name: 'employeeGlobalId', type: 'nvarchar', nullable: true, length: 127 })
  employee_global_id: string;

  @ApiProperty({ required: false })
  @Column({ name: 'employeeId', type: 'nvarchar', nullable: true, length: 127 })
  employee_id: string;

  @ApiProperty({ required: false })
  @Column({ name: 'sharpGlobalId', type: 'nvarchar', nullable: true, length: 127 })
  sharp_global_id: string;

  @ApiProperty({ required: false })
  @Column({ type: 'nvarchar' })
  email: string;

  @ApiProperty({ required: false })
  @Column({ name: 'firstName', type: 'nvarchar' })
  first_name: string;

  @ApiProperty({ required: false })
  @Column({ name: 'lastName', type: 'nvarchar' })
  last_name: string;

  @ApiProperty({ required: false })
  @Column({ name: 'isPartner', type: 'nvarchar' })
  partner_status?: partnerStatusEnum;

  @ApiProperty({ required: false })
  @Column()
  status: EmployeeStatusEnum;

  @ApiProperty({ required: false })
  @Column({ name: 'employeeBandGroup', type: 'nvarchar' })
  employee_band_group?: string;

  @ApiProperty({ required: false })
  @Column({ name: 'employeeBand', type: 'nvarchar' })
  employee_band?: string;

  @ApiProperty({ required: false })
  @Column({ name: 'function', type: 'nvarchar' })
  function?: string;

  @ApiProperty({ required: false })
  @Column({ name: 'zone', type: 'nvarchar' })
  zone: string;

  @ApiProperty({ required: false })
  @Column({ name: 'sltLevel', type: 'nvarchar' })
  slt_level?: string;

  @ApiProperty({ required: false })
  @Column({ name: 'positionTitle', type: 'nvarchar' })
  position?: string;

  @ApiProperty({ required: false })
  @ManyToOne(() => Employee, (employee) => employee.uid)
  @JoinColumn({ name: 'managerUid' })
  manager?: Employee;

  @Column({ name: 'managerUid', type: 'uniqueidentifier', nullable: true })
  managerUid?: string;

  @ApiProperty({ required: false })
  @Column({ name: 'supOrgHierarchy', type: 'nvarchar', length: 1000, nullable: true })
  hierarchy_parent_organization_id?: string;

  @Column({ name: 'isExpact', nullable: true, type: 'nvarchar' })
  is_expact?: string;
}
