import { Employee, EmployeeStatusEnum } from 'src/@core/domain/employee/Employee.entity';
import { IEmployeeRepository } from 'src/@core/domain/employee/IEmployeeRepository';
import { Repository, FindManyOptions, In, FindOneOptions } from 'typeorm';
import { ListEmployeeFiltersDTO } from 'src/@core/application/dto/listEmployeeFiltersDTO';
import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';

@Injectable()
export class EmployeeTypeOrmRepository implements IEmployeeRepository {
  constructor(
    @InjectRepository(Employee)
    private ormRepo: Repository<Employee>
  ) {}

  async findAll(filters?: ListEmployeeFiltersDTO): Promise<any> {
    const findOptions: FindManyOptions = {};

    if (filters?.filter) {
      findOptions.where = filters.filter;
    }
    if (filters?.orderBy) {
      const { field, order } = filters.orderBy;
      findOptions.order = {
        [field]: order
      };
    }
    if (filters?.pagination) {
      const { limit, page } = filters.pagination;
      findOptions.take = limit;
      findOptions.skip = (page - 1) * limit;
    }

    findOptions.relations = ['position', 'manager'];
    findOptions.select = {
      uid: true,
      employee_global_id: true,
      first_name: true,
      last_name: true,
      email: true,
      zone: true,
      function: true,
      slt_level: true,
      partner_status: true,
      employee_band: true,
      employee_band_group: true,
      manager: {
        first_name: true,
        last_name: true,
        employee_global_id: true
      },
      position: {
        name: true
      }
    };

    const [employee, count] = await this.ormRepo.findAndCount(findOptions);

    return { employee, count };
  }

  async findByUid(uid: string): Promise<Employee> {
    return this.ormRepo.findOne({
      where: { uid, status: In([EmployeeStatusEnum.ACTIVE, EmployeeStatusEnum.INACTIVE]) }
    });
  }

  async findByEmail(email: string): Promise<Employee> {
    return this.ormRepo.findOne({ where: { email, status: In([EmployeeStatusEnum.ACTIVE, EmployeeStatusEnum.INACTIVE]) } });
  }

  async findByGlobalId(id: string): Promise<Employee> {
    return this.ormRepo.findOne({
      where: [
        { employee_global_id: id, status: In([EmployeeStatusEnum.ACTIVE, EmployeeStatusEnum.INACTIVE]) },
        { employee_id: id, status: In([EmployeeStatusEnum.ACTIVE, EmployeeStatusEnum.INACTIVE]) },
        { sharp_global_id: id, status: In([EmployeeStatusEnum.ACTIVE, EmployeeStatusEnum.INACTIVE]) }
      ]
    });
  }

  private generateByIdOrEmailsOptions(id: string, upn_email: string, ad_email: string): FindOneOptions<Employee> {
    const options = {
      where: []
    };

    if (id) {
      options.where.push({ employee_global_id: id, status: In([EmployeeStatusEnum.ACTIVE, EmployeeStatusEnum.INACTIVE]) });
      options.where.push({ employee_id: id, status: In([EmployeeStatusEnum.ACTIVE, EmployeeStatusEnum.INACTIVE]) });
      options.where.push({ sharp_global_id: id, status: In([EmployeeStatusEnum.ACTIVE, EmployeeStatusEnum.INACTIVE]) });
      options.where.push({ email: ad_email, status: In([EmployeeStatusEnum.ACTIVE, EmployeeStatusEnum.INACTIVE]) });
    } else if (upn_email || ad_email) {
      options.where.push({
        email: In([upn_email, ad_email].filter((email) => email)), // filter to remove undefined/null emails
        status: In([EmployeeStatusEnum.ACTIVE, EmployeeStatusEnum.INACTIVE])
      });
    }

    return options;
  }

  async findByIdOrEmails(id: string, upn_email: string, ad_email: string): Promise<Employee> {
    return this.ormRepo.findOne(this.generateByIdOrEmailsOptions(id, upn_email, ad_email));
  }

  async isEmployeeManager(employeeId: string): Promise<boolean> {
    return this.ormRepo.exist({
      where: {
        managerUid: employeeId
      }
    });
  }
}
