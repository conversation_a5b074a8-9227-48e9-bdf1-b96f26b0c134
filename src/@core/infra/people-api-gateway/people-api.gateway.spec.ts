import { Test, TestingModule } from '@nestjs/testing';
import { PeopleApiGateway } from './people-api.gateway';
import { AxiosInstance } from 'axios';
import { SystemEnum } from 'abi-opr-common-types';

describe('MeetingDetailController', () => {
  let gateway: PeopleApiGateway;
  let axiosInstance: AxiosInstance;
  const jwtMock = 'jwt';

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        PeopleApiGateway,
        {
          provide: 'AxiosPeopleApi',
          useValue: {
            get: jest.fn().mockResolvedValue([])
          }
        }
      ]
    }).compile();

    gateway = module.get<PeopleApiGateway>(PeopleApiGateway);
    axiosInstance = module.get<AxiosInstance>('AxiosPeopleApi');
  });

  it('should be defined', () => {
    expect(gateway).toBeDefined();
  });

  describe('getRoles', () => {
    it('should call the get method correctly', async () => {
      const system = SystemEnum.OPR;
      const uri = `/employee/roles?system=${system}`;
      await gateway.getRoles(jwtMock, system);

      expect(axiosInstance.get).toBeCalledTimes(1);
      expect(axiosInstance.get).toBeCalledWith(uri, { headers: { Authorization: jwtMock } });
    });
  });
});
