import { Module } from '@nestjs/common';
import axios from 'axios';
import { config } from 'dotenv';
import { PeopleApiGateway } from './people-api.gateway';
import { IPeopleApiGatewayKey } from '@/@core/domain/people-api-gateway/IPeopleApiGateway';

config();

@Module({
  providers: [
    {
      provide: 'AxiosPeopleApi',
      useFactory: () => {
        return axios.create({
          baseURL: process.env.PEOPLE_PLATFORM_API_URL
        });
      }
    },
    {
      provide: IPeopleApiGatewayKey,
      useClass: PeopleApiGateway
    }
  ],
  exports: [
    {
      provide: IPeopleApiGatewayKey,
      useClass: PeopleApiGateway
    }
  ]
})
export class PeopleApiModule {}
