import { Inject, Injectable } from '@nestjs/common';

import { AxiosInstance } from 'axios';
import { SystemEnum } from 'abi-opr-common-types';
import { IPeopleApiGateway } from '@/@core/domain/people-api-gateway/IPeopleApiGateway';

@Injectable()
export class PeopleApiGateway implements IPeopleApiGateway {
  private readonly rolesUri = (system: SystemEnum) => `/employee/roles?system=${system}`;

  constructor(@Inject('AxiosPeopleApi') private readonly axiosInstance: AxiosInstance) {}

  async getRoles(authorization: string, system: SystemEnum): Promise<string[]> {
    try {
      const roles = await this.axiosInstance.get<string[]>(this.rolesUri(system), {
        headers: { Authorization: authorization }
      });

      return roles.data;
    } catch (error) {
      console.error(`[${PeopleApiGateway.name}] - ${JSON.stringify(error)}`);
      throw error;
    }
  }
}
