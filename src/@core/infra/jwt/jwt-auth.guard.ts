import { IS_PUBLIC_KEY } from '@/@core/application/decorators/public.decorator';
import { ExecutionContext, Injectable, UnauthorizedException } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { JwtService } from '@nestjs/jwt';
import { AuthGuard } from '@nestjs/passport';
import { IncomingMessage as Request } from 'http';

declare global {
  type IncomingMessage = Request & { user: string | { [key: string]: any } } & { route: { path: string } };
}

@Injectable()
export class JwtAuthGuard extends AuthGuard('jwt') {
  constructor(
    private reflector: Reflector,
    private jwtService: JwtService
  ) {
    super();
  }

  canActivate(context: ExecutionContext): boolean {
    const isPublic = this.reflector.getAllAndOverride<boolean>(IS_PUBLIC_KEY, [context.getHandler(), context.getClass()]);

    if (isPublic) {
      return true;
    }

    const httpContext = context.switchToHttp();
    const request = httpContext.getRequest() as IncomingMessage;
    const authorization = request.headers.authorization?.replace('Bearer ', '');
    try {
      const decoded = this.jwtService.decode(authorization);
      request.user = decoded;

      if (authorization) {
        let prefix = process.env.GLOBAL_PREFIX;
        prefix = prefix.startsWith('/') ? prefix.substring(1) : prefix;
        prefix = prefix.endsWith('/') ? prefix.substring(0, prefix.length - 1) : prefix;
        const routeMatch = `/${prefix}/auth/refresh`;
        const routeV2Match = `/${prefix}/v2/auth/refresh`;

        if (routeMatch === request.route.path || routeV2Match === request.route.path) {
          if (this.jwtService.verify(authorization, { secret: process.env.JWT_REFRESH_SECRET })) return true;
        } else {
          if (this.jwtService.verify(authorization, { secret: process.env.JWT_SECRET })) return true;
        }
      }
    } catch (ignore) {
      return false;
    }
  }

  handleRequest(err, user, info) {
    console.log(info);
    if (err || !user) {
      throw err || new UnauthorizedException();
    }
    return user;
  }
}
