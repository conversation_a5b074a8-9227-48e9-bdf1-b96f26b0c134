import { ExtractJwt, Strategy } from 'passport-jwt';
import { Injectable } from '@nestjs/common';
import { PassportStrategy } from '@nestjs/passport';
import { DecodedJwtToken } from 'abi-opr-common-types';
import { TokenDataDto } from '@/@core/application/dto/tokenData.dto';

@Injectable()
export class JwtStrategy extends PassportStrategy(Strategy) {
  constructor() {
    super({
      jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
      ignoreExpiration: false,
      secretOrKey: process.env.JWT_SECRET
    });
  }

  async validate(payload: DecodedJwtToken): Promise<TokenDataDto> {
    return {
      userId: payload.uid,
      sub: payload.sub,
      inBehalfOf: payload.inBehalfOf,
      isManager: payload.isManager
    };
  }
}
