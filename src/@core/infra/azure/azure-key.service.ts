import { Inject, Injectable } from '@nestjs/common';
import { AxiosInstance } from 'axios';
import { AzureAxiosKey, IAzureKey, IAzureKeysResponse } from './interfaces/azure-keys-response.interface';

@Injectable()
export class AzureKeyService {
  private readonly azureBaseUrl = new URL('https://login.microsoftonline.com');

  constructor(@Inject(AzureAxiosKey) private readonly axiosInstance: AxiosInstance) {}

  async getKeysByTenantId(tenantId: string): Promise<IAzureKey[]> {
    this.azureBaseUrl.pathname = `/${tenantId}/discovery/keys`;
    const { data } = await this.axiosInstance.get<IAzureKeysResponse>(this.azureBaseUrl.toString());
    return data.keys;
  }
}
