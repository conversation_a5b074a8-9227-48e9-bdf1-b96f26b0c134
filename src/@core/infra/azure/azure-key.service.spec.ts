import { Test, TestingModule } from '@nestjs/testing';
import { AzureAxiosKey } from './interfaces/azure-keys-response.interface';
import { AzureKeyService } from './azure-key.service';

describe('AzureKeyService', () => {
  let service: AzureKeyService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        AzureKeyService,
        {
          provide: AzureAxiosKey,
          useValue: {
            get: jest.fn()
          }
        }
      ]
    }).compile();

    service = module.get<AzureKeyService>(AzureKeyService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  it('should return keys when given a valid tenantId', async () => {
    const tenantId = 'validTenantId';
    const mockKeysResponse = {
      keys: [{}]
    };

    jest.spyOn(service['axiosInstance'], 'get').mockResolvedValueOnce({ data: mockKeysResponse });

    const result = await service.getKeysByTenantId(tenantId);

    expect(result).toEqual(mockKeysResponse.keys);
  });

  it('should throw an error when the request fails', async () => {
    const tenantId = 'invalidTenantId';

    jest.spyOn(service['axiosInstance'], 'get').mockRejectedValueOnce(new Error('Request failed'));

    await expect(service.getKeysByTenantId(tenantId)).rejects.toThrowError('Request failed');
  });

  it('should make a request with the correct URL', async () => {
    const tenantId = 'testTenantId';
    const expectedUrl = `https://login.microsoftonline.com/${tenantId}/discovery/keys`;

    jest.spyOn(service['axiosInstance'], 'get').mockResolvedValueOnce({ data: { keys: [] } });

    await service.getKeysByTenantId(tenantId);

    expect(service['axiosInstance'].get).toHaveBeenCalledWith(expectedUrl);
  });
});
