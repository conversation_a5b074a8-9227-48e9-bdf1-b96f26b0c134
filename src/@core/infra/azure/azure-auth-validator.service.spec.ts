import { Test, TestingModule } from '@nestjs/testing';
import { AzureAuthValidatorService } from './azure-auth-validator.service';
import { AzureKeyService } from './azure-key.service';
import * as jwt from 'jsonwebtoken';
import { UnauthorizedException } from '@nestjs/common';
import { IAzureKey } from './interfaces/azure-keys-response.interface';
import { AzureJwtPayload } from './interfaces/jwt-decoded.interface';

xdescribe('AzureAuthValidatorService', () => {
  let service: AzureAuthValidatorService;
  const jwtToken = 'invalidToken';
  const decodedTokenMock = {
    header: { kid: 'kid' },
    payload: {
      exp: Math.floor(Date.now() / 1000) + 3600,
      tid: 'tenantId'
    }
  };
  const azureKeysMock: IAzureKey[] = [{ kid: decodedTokenMock.header.kid, x5c: [''], e: '', kty: '', n: '', use: '', x5t: '' }];

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        AzureAuthValidatorService,
        {
          provide: AzureKeyService,
          useValue: {
            getKeysByTenantId: jest.fn()
          }
        }
      ]
    }).compile();

    service = module.get<AzureAuthValidatorService>(AzureAuthValidatorService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  it('should authenticate successfully', async () => {
    const mockResponse: AzureJwtPayload = { email: 'emailFake' } as AzureJwtPayload;

    jest.spyOn(jwt, 'decode').mockReturnValueOnce(decodedTokenMock);
    jest.spyOn(service['azureKeyService'], 'getKeysByTenantId').mockResolvedValueOnce(azureKeysMock);
    jest.spyOn(jwt, 'verify').mockReturnValueOnce(mockResponse as any);

    const result = await service.authenticate(jwtToken);

    expect(result).toEqual(mockResponse);
  });

  it('should throw UnauthorizedException for expired token', async () => {
    const mockExp = Math.floor(Date.now() / 1000) - 3600;

    jest.spyOn(jwt, 'decode').mockReturnValueOnce({
      header: {
        kid: decodedTokenMock.header.kid
      },
      payload: {
        exp: mockExp,
        tid: decodedTokenMock.payload.tid
      }
    });

    await expect(service.authenticate(jwtToken)).rejects.toThrowError(new UnauthorizedException('Expired token'));
  });

  it('should throw UnauthorizedException for invalid token', async () => {
    jest.spyOn(jwt, 'decode').mockReturnValueOnce({ payload: { exp: decodedTokenMock.payload.exp } });

    await expect(service.authenticate(jwtToken)).rejects.toThrowError(new UnauthorizedException('Invalid token'));
  });

  it('should throw UnauthorizedException if key is not found for the given kid', async () => {
    const azureKeysMock: IAzureKey[] = [{ kid: '', x5c: [''], e: '', kty: '', n: '', use: '', x5t: '' }];
    jest.spyOn(jwt, 'decode').mockReturnValueOnce(decodedTokenMock);
    jest.spyOn(service['azureKeyService'], 'getKeysByTenantId').mockResolvedValueOnce(azureKeysMock);

    await expect(service.authenticate(jwtToken)).rejects.toThrowError(new UnauthorizedException('Token signed with unknown key'));
  });

  it('should throw UnauthorizedException for JWT verification error', async () => {
    jest.spyOn(jwt, 'decode').mockReturnValueOnce(decodedTokenMock);
    jest.spyOn(service['azureKeyService'], 'getKeysByTenantId').mockResolvedValueOnce(azureKeysMock);

    await expect(service.authenticate(jwtToken)).rejects.toThrowError(new UnauthorizedException('Error verifying JWT token'));
  });
});
