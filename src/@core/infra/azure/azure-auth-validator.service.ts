import * as jwt from 'jsonwebtoken';
import { Injectable, Logger, UnauthorizedException } from '@nestjs/common';
import { IAzureKey } from './interfaces/azure-keys-response.interface';
import { AzureJwtDecoded, AzureJwtPayload } from './interfaces/jwt-decoded.interface';
import { AzureKeyService } from './azure-key.service';

@Injectable()
export class AzureAuthValidatorService {
  private kid: string;
  private tenantId: string;
  logger: Logger;

  constructor(private readonly azureKeyService: AzureKeyService) {
    this.logger = new Logger(AzureAuthValidatorService.name);
  }

  async authenticate(jwtToken: string): Promise<AzureJwtPayload> {
    const authorization = jwtToken.replace('Bearer ', '');
    const decodedToken = jwt.decode(authorization, { complete: true }) as AzureJwtDecoded;

    this.validateTokenParameters(decodedToken);
    this.tokenIsExpired(decodedToken);

    this.logger.log(`Azure AD auth tenantId: ${this.tenantId}`);
    const keys = await this.azureKeyService.getKeysByTenantId(this.tenantId);
    const key = this.findKeyByKid(keys, this.kid);
    const tokenValidate = this.verifyJWT(key, authorization);
    return tokenValidate;
  }

  private validateTokenParameters(decodedToken: AzureJwtDecoded): void {
    this.tenantId = decodedToken?.payload?.tid;
    this.kid = decodedToken?.header?.kid;

    if (!this.tenantId || !this.kid) {
      this.logger.error('Invalid token');
      throw new UnauthorizedException('Invalid token');
    }
  }

  private tokenIsExpired(decodedToken: AzureJwtDecoded): void {
    if (!decodedToken.payload?.exp || decodedToken.payload?.exp * 1000 < Date.now()) {
      this.logger.error('Expired token');
      throw new UnauthorizedException('Expired token');
    }
  }

  private generateCertificate(key: string): string {
    const certificateStart = '-----BEGIN CERTIFICATE-----';
    const certificateEnd = '-----END CERTIFICATE-----';

    return `${certificateStart}\n${key}\n${certificateEnd}`;
  }

  private findKeyByKid(keys: IAzureKey[], kid: string): string {
    const key = keys.find((k: any) => k.kid === kid);

    if (!key) {
      this.logger.error('Public key not found for the given kid');
      throw new UnauthorizedException('Token signed with unknown key');
    }

    return key.x5c[0];
  }

  private verifyJWT(key: string, token: string): AzureJwtPayload {
    try {
      const verify = jwt.verify(token, this.generateCertificate(key));
      return verify as AzureJwtPayload;
    } catch (error) {
      this.logger.error('Error verifying JWT token');
      throw new UnauthorizedException('Error verifying JWT token');
    }
  }
}
