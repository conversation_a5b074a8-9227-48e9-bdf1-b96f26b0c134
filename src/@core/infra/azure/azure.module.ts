import { Module } from '@nestjs/common';
import axios from 'axios';
import { AzureAxiosKey } from './interfaces/azure-keys-response.interface';
import { AzureAuthValidatorService } from './azure-auth-validator.service';
import { AzureKeyService } from './azure-key.service';

@Module({
  providers: [
    AzureAuthValidatorService,
    AzureKeyService,
    {
      provide: AzureAxiosKey,
      useFactory: () => {
        return axios.create();
      }
    }
  ],
  exports: [AzureAuthValidatorService]
})
export class AzureModule {}
