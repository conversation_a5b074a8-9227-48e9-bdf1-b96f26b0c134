import { IPeopleApiGateway, IPeopleApiGatewayKey } from '@/@core/domain/people-api-gateway/IPeopleApiGateway';
import { <PERSON><PERSON><PERSON><PERSON>, ExecutionContext, Inject, Injectable, NestInterceptor, UnauthorizedException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { DecodedJwtToken2, SecurityRoleTypeEnum, SystemEnum } from 'abi-opr-common-types';
import { Observable } from 'rxjs';

@Injectable()
export class ProxyInterceptor implements NestInterceptor {
  private readonly PROXY_PERMISSIONS: { app: SystemEnum; requiredRoles: SecurityRoleTypeEnum[] }[];

  constructor(
    @Inject(IPeopleApiGatewayKey) private readonly peopleApiGateway: IPeopleApiGateway,
    private readonly config: ConfigService
  ) {
    const base = [
      { app: SystemEnum.OPR, requiredRoles: [SecurityRoleTypeEnum.GLOBAL_ADMIN] },
      {
        app: SystemEnum.REWARDS,
        requiredRoles: [
          SecurityRoleTypeEnum.GLOBAL_ADMIN,
          SecurityRoleTypeEnum.REWARDS_ADMIN,
          SecurityRoleTypeEnum.REWARDS_ZONE_ADMIN
        ]
      },
      { app: SystemEnum.LCM, requiredRoles: [SecurityRoleTypeEnum.LCM_GLOBAL_ADMIN] },
      { app: SystemEnum.INSIGHTSHUB, requiredRoles: [SecurityRoleTypeEnum.INSIGHTSHUB_GLOBAL_ADMIN] },
      { app: SystemEnum.ONEVERSE, requiredRoles: [SecurityRoleTypeEnum.GLOBAL_ADMIN] },
      { app: SystemEnum.NORTHSTAR, requiredRoles: [] },
    ];

    const env = this.config.get<string>('NODE_ENV', 'development').toLowerCase();

    if (env !== 'production') {
      base.push({
        app: SystemEnum.COMPLIANCE,
        requiredRoles: [SecurityRoleTypeEnum.GLOBAL_ADMIN]
      });
    }

    this.PROXY_PERMISSIONS = base;
  }

  async intercept(context: ExecutionContext, next: CallHandler): Promise<Observable<any>> {
    const request = context.switchToHttp().getRequest();
    const authorization = request.headers.authorization;

    const decodedToken = request.user as DecodedJwtToken2;

    const system = decodedToken.system || SystemEnum.OPR;

    const roles = await this.peopleApiGateway.getRoles(authorization, system);

    const proxyPermission = this.PROXY_PERMISSIONS.find((permissions) => permissions.app === system);

    const hasPermission = proxyPermission.requiredRoles.length === 0 || roles.some((role) => proxyPermission.requiredRoles.includes(role as SecurityRoleTypeEnum));

    if (hasPermission) return next.handle();

    throw new UnauthorizedException('Unauthorized access to proxy');
  }
}
