import { Employee } from '@/@core/domain/employee/Employee.entity';
import { EmployeeRepositoryKey, IEmployeeRepository } from '@/@core/domain/employee/IEmployeeRepository';
import { Inject, Injectable, Logger, NotFoundException } from '@nestjs/common';

@Injectable()
export default class GetEmployeeByIdUseCase {
  logger: Logger;

  constructor(
    @Inject(EmployeeRepositoryKey)
    private readonly employeeRepo: IEmployeeRepository
  ) {
    this.logger = new Logger(GetEmployeeByIdUseCase.name);
  }

  async execute(employeeId: string): Promise<Employee> {
    const employee = await this.employeeRepo.findByUid(employeeId);
    if (!employee) {
      this.logger.error(`User not found employeeId: ${employeeId}`);
      throw new NotFoundException('User not found');
    }
    return employee;
  }
}
