import { Test, TestingModule } from '@nestjs/testing';
import { EmployeeRepositoryKey, IEmployeeRepository } from '@/@core/domain/employee/IEmployeeRepository';
import { NotFoundException } from '@nestjs/common';
import GetEmployeeByGlobalIdUseCase from './GetEmployeeByGlobalId.use-case';

xdescribe('GetEmployeeByGlobalUseCase', () => {
  let useCase: GetEmployeeByGlobalIdUseCase;
  let repository: IEmployeeRepository;

  const mockEmployee = {
    employee_global_id: 'employee123'
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        GetEmployeeByGlobalIdUseCase,
        {
          provide: EmployeeRepositoryKey,
          useValue: {
            findByGlobalId: jest.fn().mockResolvedValue(mockEmployee)
          }
        }
      ]
    }).compile();

    useCase = module.get<GetEmployeeByGlobalIdUseCase>(GetEmployeeByGlobalIdUseCase);
    repository = module.get<IEmployeeRepository>(EmployeeRepositoryKey);
  });

  it('should be defined', () => {
    expect(useCase).toBeDefined();
  });

  it('should return an employee when a valid globalId is provided', async () => {
    const result = await useCase.execute(mockEmployee.employee_global_id);

    expect(result).toBeDefined();
    expect(result.employee_global_id).toBe(mockEmployee.employee_global_id);
  });

  it('should throw a NotFoundException when an invalid globalId is provided', async () => {
    jest.spyOn(repository, 'findByGlobalId').mockResolvedValue(null);
    await expect(useCase.execute(mockEmployee.employee_global_id)).rejects.toThrowError(new NotFoundException('User not found'));
  });
});
