import { Test, TestingModule } from '@nestjs/testing';
import { EmployeeRepositoryKey, IEmployeeRepository } from '@/@core/domain/employee/IEmployeeRepository';
import { NotFoundException } from '@nestjs/common';
import GetEmployeeByIdUseCase from './GetEmployeeById.use-case';

xdescribe('GetEmployeeByIdUseCase', () => {
  let useCase: GetEmployeeByIdUseCase;
  let repository: IEmployeeRepository;

  const mockEmployee = {
    uid: '123'
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        GetEmployeeByIdUseCase,
        {
          provide: EmployeeRepositoryKey,
          useValue: {
            findByUid: jest.fn().mockResolvedValue(mockEmployee)
          }
        }
      ]
    }).compile();

    useCase = module.get<GetEmployeeByIdUseCase>(GetEmployeeByIdUseCase);
    repository = module.get<IEmployeeRepository>(EmployeeRepositoryKey);
  });

  it('should be defined', () => {
    expect(useCase).toBeDefined();
  });

  it('should return an employee when a valid employeeId is provided', async () => {
    const result = await useCase.execute(mockEmployee.uid);

    expect(result).toBeDefined();
    expect(result.uid).toBe(mockEmployee.uid);
  });

  it('should throw a NotFoundException when an invalid employeeId is provided', async () => {
    jest.spyOn(repository, 'findByUid').mockResolvedValue(null);
    await expect(useCase.execute(mockEmployee.uid)).rejects.toThrowError(new NotFoundException('User not found'));
  });
});
