import { Employee } from '@/@core/domain/employee/Employee.entity';
import { EmployeeRepositoryKey, IEmployeeRepository } from '@/@core/domain/employee/IEmployeeRepository';
import { Inject, Injectable, Logger, NotFoundException } from '@nestjs/common';

@Injectable()
export default class GetEmployeeByGlobalIdUseCase {
  logger: Logger;

  constructor(
    @Inject(EmployeeRepositoryKey)
    private readonly employeeRepo: IEmployeeRepository
  ) {
    this.logger = new Logger(GetEmployeeByGlobalIdUseCase.name);
  }

  async execute(globalId: string): Promise<Employee> {
    const employee = await this.employeeRepo.findByGlobalId(globalId);
    if (!employee) {
      this.logger.error(`User not found globalId: ${globalId}`);
      throw new NotFoundException('User not found');
    }
    return employee;
  }
}
