import { ProxyRulesUseCase } from './ProxyRules.use-case';

const makeSut = () => {
  const sut = new ProxyRulesUseCase();
  return {
    sut
  };
};

describe('ProxyRulesUseCase Tests', () => {
  it('should return the behalf of as the original user', async () => {
    const { sut } = makeSut();
    const behalf = sut.checkInBehalfOf('original_id', 'original_id');
    expect(behalf).toBe('original_id');
  });

  it('should return the behalf of as the proxy user', async () => {
    const { sut } = makeSut();
    const behalf = sut.checkInBehalfOf('proxy_user_id', 'original_id');
    expect(behalf).toBe('proxy_user_id');
  });
});
