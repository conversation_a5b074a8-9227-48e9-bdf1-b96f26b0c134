import { AuthenticateInput } from '@/@core/types/authentication-data.type';
import { EmployeeRepositoryStub } from '../mocks/MockEmployeeRepository';
import { AuthenticateEmployeeUseCase } from './Authenticate.use-case';
import { Employee, EmployeeStatusEnum } from '@/@core/domain/employee/Employee.entity';

const makeSut = () => {
  const employeeRepo = new EmployeeRepositoryStub();
  const sut = new AuthenticateEmployeeUseCase(employeeRepo);
  return {
    sut,
    employeeRepo
  };
};

describe('AuthenticateEmployeeUseCase Tests', () => {
  let fakeEmployee: Employee;
  let fakeRequest: AuthenticateInput;

  beforeEach(async () => {
    fakeEmployee = {
      employee_global_id: 'correct_global_id',
      employee_id: 'correct_global_id',
      sharp_global_id: 'correct_global_id',
      email: '<EMAIL>',
      uid: 'correct_uid',
      first_name: 'test',
      last_name: 'employee',
      status: EmployeeStatusEnum.ACTIVE,
      zone: 'GLOBAL',
      createdAt: new Date(),
      updatedAt: new Date()
    };

    fakeRequest = {
      ad_email: '<EMAIL>',
      global_id: 'correct_global_id',
      upn_email: '<EMAIL>'
    };
  });

  it('should be able to authenticate by finding an employee through global_id', async () => {
    const { sut, employeeRepo } = makeSut();

    jest.spyOn(employeeRepo, 'findByIdOrEmails').mockResolvedValueOnce(fakeEmployee);

    const employee = await sut.execute(fakeRequest);

    expect(employee).toEqual(fakeEmployee);
  });

  it('should be able to authenticate employee by finding an employee through upn_email', async () => {
    const { sut, employeeRepo } = makeSut();

    jest.spyOn(employeeRepo, 'findByIdOrEmails').mockResolvedValueOnce(fakeEmployee);
    delete fakeRequest.global_id;
    delete fakeRequest.ad_email;
    const employee = await sut.execute(fakeRequest);

    expect(employee).toEqual(fakeEmployee);
  });

  it('should be able to authenticate employee by finding an employee through ad_email', async () => {
    const { sut, employeeRepo } = makeSut();

    jest.spyOn(employeeRepo, 'findByIdOrEmails').mockResolvedValueOnce(fakeEmployee);
    delete fakeRequest.global_id;
    delete fakeRequest.upn_email;
    const employee = await sut.execute(fakeRequest);

    expect(employee).toEqual(fakeEmployee);
  });

  it('should receive null when employee is not found', async () => {
    const { sut, employeeRepo } = makeSut();

    jest.spyOn(employeeRepo, 'findByIdOrEmails').mockResolvedValueOnce(null);
    delete fakeRequest.global_id;
    delete fakeRequest.upn_email;
    const employee = await sut.execute(fakeRequest);

    expect(employee).toBe(null);
  });
});
