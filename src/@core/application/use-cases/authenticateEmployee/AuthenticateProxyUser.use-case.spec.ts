import { EmployeeRepositoryStub } from '../mocks/MockEmployeeRepository';
import { AuthenticateEmployeeByIdUseCase } from './AuthenticateEmployeeById.use-case';
import { Employee, EmployeeStatusEnum } from '@/@core/domain/employee/Employee.entity';

const makeSut = () => {
  const employeeRepo = new EmployeeRepositoryStub();
  const sut = new AuthenticateEmployeeByIdUseCase(employeeRepo);
  return {
    sut,
    employeeRepo
  };
};

describe('AuthenticateProxyUserUseCase Tests', () => {
  let fakeEmployee: Employee;

  beforeEach(async () => {
    fakeEmployee = {
      employee_global_id: 'correct_global_id',
      employee_id: 'correct_global_id',
      sharp_global_id: 'correct_global_id',
      email: '<EMAIL>',
      uid: 'correct_uid',
      first_name: 'test',
      last_name: 'employee',
      status: EmployeeStatusEnum.ACTIVE,
      zone: 'GLOBAL',
      createdAt: new Date(),
      updatedAt: new Date()
    };
  });

  it('should be able to authenticate by finding an employee id', async () => {
    const { sut, employeeRepo } = makeSut();

    jest.spyOn(employeeRepo, 'findByUid').mockResolvedValueOnce(fakeEmployee);

    const employee = await sut.execute({ type: 'id', value: 'id' });

    expect(employee).toEqual(fakeEmployee);
  });
});
