import { Inject } from '@nestjs/common';
import { EmployeeRepositoryKey, IEmployeeRepository } from '@/@core/domain/employee/IEmployeeRepository';
import { Employee } from 'src/@core/domain/employee/Employee.entity';

type EmployeeIdentifier = { type: 'employee_global_id'; value: string } | { type: 'id'; value: string };

export class AuthenticateEmployeeByIdUseCase {
  constructor(
    @Inject(EmployeeRepositoryKey)
    private employeeRepo: IEmployeeRepository
  ) {}

  async execute(data: EmployeeIdentifier): Promise<Employee> {
    if (data.type === 'employee_global_id') {
      return await this.employeeRepo.findByGlobalId(data.value);
    } else {
      return await this.employeeRepo.findByUid(data.value);
    }
  }
}
