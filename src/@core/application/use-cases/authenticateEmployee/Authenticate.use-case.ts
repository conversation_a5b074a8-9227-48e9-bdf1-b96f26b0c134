import { Inject } from '@nestjs/common';
import { EmployeeRepositoryKey, IEmployeeRepository } from '@/@core/domain/employee/IEmployeeRepository';
import { Employee } from 'src/@core/domain/employee/Employee.entity';
import { AuthenticateInput } from 'src/@core/types/authentication-data.type';

export class AuthenticateEmployeeUseCase {
  constructor(
    @Inject(EmployeeRepositoryKey)
    private readonly employeeRepo: IEmployeeRepository
  ) {}

  async execute({ global_id, upn_email, ad_email }: AuthenticateInput): Promise<Employee> {
    let employee: Employee = null;

    employee = await this.employeeRepo.findByIdOrEmails(global_id, upn_email, ad_email);

    return employee;
  }
}
