import { Employee } from '@/@core/domain/employee/Employee.entity';
import { ServiceBusClient, ServiceBusSender } from '@azure/service-bus';
import { Inject } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

export class EmployeeAdoptionUseCase {
  sbClient: ServiceBusClient;
  sbSender: ServiceBusSender;

  constructor(
    @Inject(ConfigService)
    private readonly configService: ConfigService
  ) {
    // initialize Service Bus
    this.sbClient = new ServiceBusClient(configService.get('SERVICE_BUS_EMPLOYEE_ADOPTION'));
    this.sbSender = this.sbClient.createSender(configService.get('SERVICE_BUS_EMPLOYEE_ADOPTION_TOPIC'));
  }

  async execute(user: Employee, system: string): Promise<any> {
    return this.sbSender.sendMessages({
      body: {
        ...user,
        system,
        date: new Date().toISOString()
      }
    });
  }
}
