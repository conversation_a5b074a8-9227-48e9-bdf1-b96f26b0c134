import { Employee } from '@/@core/domain/employee/Employee.entity';
import { EmployeeStatusEnum } from 'abi-opr-common-types';
import { EmployeeAdoptionUseCase } from './EmployeeAdoption.use-case';

const makeSut = () => {
  const configService = jest.fn().mockImplementation(() => ({
    get: jest.fn((key: string) => {
      if (key === 'SERVICE_BUS_EMPLOYEE_ADOPTION') {
        return 'Endpoint=sb://sbname.servicebus.windows.net/;SharedAccessKeyName=keyname;SharedAccessKey=sharedaccesskey;EntityPath=platform-adoption';
      }
      if (key === 'SERVICE_BUS_EMPLOYEE_ADOPTION_TOPIC') {
        return 'platform-adoption';
      }
      return null;
    })
  }));
  const configServiceInstance = new configService();
  const sut = new EmployeeAdoptionUseCase(configServiceInstance);
  return {
    sut,
    configService
  };
};

describe('EmployeeAdoptionUseCase Tests', () => {
  let fakeEmployee: Employee;
  let fakeSystem: string;

  beforeEach(async () => {
    fakeEmployee = {
      employee_global_id: 'correct_global_id',
      employee_id: 'correct_global_id',
      sharp_global_id: 'correct_global_id',
      email: '<EMAIL>',
      uid: 'correct_uid',
      first_name: 'test',
      last_name: 'employee',
      status: EmployeeStatusEnum.ACTIVE,
      zone: 'GLOBAL',
      employee_band: 'A',
      createdAt: new Date(),
      updatedAt: new Date()
    };

    fakeSystem = 'testSystem';
  });

  it('should be able to send a message to the service bus', async () => {
    const { sut } = makeSut();
    const sendSpy = jest.spyOn(sut.sbSender, 'sendMessages').mockResolvedValueOnce();

    await sut.execute(fakeEmployee, fakeSystem);

    expect(sendSpy).toHaveBeenCalledWith({
      body: {
        ...fakeEmployee,
        system: fakeSystem,
        date: expect.any(String)
      }
    });
  });
});
