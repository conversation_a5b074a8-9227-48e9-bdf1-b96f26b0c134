import { JwtService } from '@nestjs/jwt';
import { Employee } from 'src/@core/domain/employee/Employee.entity';
import { DecodedJwtToken } from 'abi-opr-common-types';
import { EmployeeRepositoryKey, IEmployeeRepository } from '@/@core/domain/employee/IEmployeeRepository';
import { Inject } from '@nestjs/common';

export class CreateTokenUseCase {
  constructor(
    private jwtService: JwtService,
    @Inject(EmployeeRepositoryKey)
    private employeeRepository: IEmployeeRepository
  ) {}

  async execute(employee: Employee, behalfOf?: string): Promise<[string | undefined, string | undefined]> {
    if (employee) {
      const isManager = await this.employeeRepository.isEmployeeManager(employee.uid);
      const payload: Partial<DecodedJwtToken> & { isManager: boolean; bandGroup: string } = {
        globalId: employee.employee_global_id || '',
        bandGroup: employee.employee_band_group || '',
        sub: employee.email,
        uid: employee.uid,
        inBehalfOf: behalfOf,
        isManager
      };
      const [token, refreshToken] = await Promise.all([
        this.jwtService.signAsync(payload, { secret: process.env.JWT_SECRET, expiresIn: process.env.JWT_EXP || '15m' }),
        this.jwtService.signAsync(
          {
            uid: employee.uid,
            globalId: employee.employee_global_id || '',
            sub: employee.email,
            inBehalfOf: behalfOf,
            isRefreshToken: true
          },
          { secret: process.env.JWT_REFRESH_SECRET, expiresIn: process.env.JWT_REFRESH_EXP || '7d' }
        )
      ]);

      return [token, refreshToken];
    }
    return [undefined, undefined];
  }
}
