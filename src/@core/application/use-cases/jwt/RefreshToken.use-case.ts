import { Injectable } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { DecodedJwtToken } from 'abi-opr-common-types';
import { Employee } from 'src/@core/domain/employee/Employee.entity';
import { AuthenticateInput } from 'src/@core/types/authentication-data.type';
import { AuthenticateEmployeeUseCase } from '../authenticateEmployee/Authenticate.use-case';
import { ProxyRulesUseCase } from '../proxyRules/ProxyRules.use-case';

@Injectable()
export default class RefreshTokenUseCase {
  constructor(
    private readonly jwtService: JwtService,
    private readonly authenticateEmployeeUseCase: AuthenticateEmployeeUseCase,
    private readonly proxyRulesUseCase: ProxyRulesUseCase
  ) {}

  async execute(refreshToken: string): Promise<[Employee, string]> {
    const { sub, globalId, uid, inBehalfOf } = this.jwtService.decode(refreshToken) as DecodedJwtToken;

    const authenticateInput: AuthenticateInput = {
      global_id: globalId || '',
      ad_email: sub,
      upn_email: sub
    };

    const realBehalfOf = this.proxyRulesUseCase.checkInBehalfOf(inBehalfOf, uid);

    const employee = await this.authenticateEmployeeUseCase.execute(authenticateInput);

    return [employee, realBehalfOf];
  }
}
