import { JwtService } from '@nestjs/jwt';
import { Employee } from 'src/@core/domain/employee/Employee.entity';
import { SystemEnum } from 'abi-opr-common-types';
import { Injectable } from '@nestjs/common';

@Injectable()
export class CreateSimplifiedTokenUseCase {
  constructor(private jwtService: JwtService) {}

  async execute(
    employee: Employee,
    system: SystemEnum,
    behalfOf: string | null | undefined
  ): Promise<[string | undefined, string | undefined]> {
    if (employee) {
      // const payload: Partial<DecodedJwtToken2> = {
      const payload = {
        globalId: employee.employee_global_id || '',
        sub: employee.email,
        uid: employee.uid,
        inBehalfOf: behalfOf,
        isRefreshToken: false,
        system
      } as any;

      const [token, refreshToken] = await Promise.all([
        this.jwtService.signAsync(payload, { secret: process.env.JWT_SECRET, expiresIn: process.env.JWT_EXP || '15m' }),
        this.jwtService.signAsync(
          {
            ...payload,
            isRefreshToken: true
          },
          { secret: process.env.JWT_REFRESH_SECRET, expiresIn: process.env.JWT_REFRESH_EXP || '7d' }
        )
      ]);

      return [token, refreshToken];
    }
    console.log(`employee not found - CreateSimplifiedTokenUseCase - system ${system}`);
    return [undefined, undefined];
  }
}
