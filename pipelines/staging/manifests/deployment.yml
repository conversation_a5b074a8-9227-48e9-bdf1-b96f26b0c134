apiVersion: apps/v1
kind: Deployment
metadata:
  namespace: "people-product-stg"
  name: "auth-api"
  labels:
    k8s-app: "auth-api" 
spec:
  selector:
    matchLabels:
      k8s-app: "auth-api"
  replicas: 1
  template:
    metadata:
      labels:
        k8s-app: "auth-api"
    spec:
      containers:
        - name: "auth-api"
          image: peopleproductsacr.azurecr.io/auth-api
          ports:
          - containerPort: 3000
          env:
            - name: COSMOS_URL
              value: people-api-cosmosdb-url@azurekeyvault
            - name: COSMOS_KEY
              value: people-api-cosmosdb-key@azurekeyvault
            - name: COSMOS_DB_NAME
              value: EmployeePermission
            - name: COSMOS_CONTAINER_NAME
              value: permissions
            - name: DB_HOST
              value: people-api-stg-database-host@azurekeyvault
            - name: DB_USER
              value: people-api-stg-database-user@azurekeyvault
            - name: DB_PASSWORD
              value: people-api-stg-database-password@azurekeyvault
            - name: DB_NAME
              value: people-api-stg-database-dbname@azurekeyvault
            - name: DB_SCHEMA
              value: people-api-stg-database-schema@azurekeyvault
            - name: JWT_SECRET
              value: secretjwt@azurekeyvault
            - name: JWT_REFRESH_SECRET
              value: refreshsecretjwt@azurekeyvault
            - name: JWT_EXP
              value: 1d
            - name: CORS_ORIGIN
              value: https://test.opr.ab-inbev.com/
            - name: GLOBAL_PREFIX
              value: /api/auth-people-platform
            - name: COSMOS_DB_NAME
              value: EmployeePermission
            - name: COSMOS_CONTAINER_NAME
              value: permissions
            - name: REQUESTER_APP_KEY_OPR
              value: requester-app-key-opr-stg@azurekeyvault
            - name: REQUESTER_APP_KEY_REWARDS
              value: requester-app-key-rewards-stg@azurekeyvault
            - name: PEOPLE_PLATFORM_API_URL
              value: http://people-platform-api/api/people-platform/
            - name: SERVICE_BUS_EMPLOYEE_ADOPTION
              value: platform-auth-api-service-bus-employee-adoption-stg@azurekeyvault
            - name: SERVICE_BUS_EMPLOYEE_ADOPTION_TOPIC
              value: "platform-adoption"        
            - name: DD_ENV
              value: 'stg'      
          volumeMounts:
          - name: token
            mountPath: "var/run/secrets/kubernetes.io/serviceaccount/"
            readOnly: true
      imagePullSecrets:
        - name: acr-registry
      serviceAccountName: service-account-platform-auth-api-stg
      volumes:
        - name: token
          secret:
            secretName: service-account-people-produts-token-vlf7q