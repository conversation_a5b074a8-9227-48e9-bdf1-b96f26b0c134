apiVersion: apps/v1
kind: Deployment
metadata:
  namespace: "people-product-prod"
  name: "auth-api"
  labels:
    k8s-app: "auth-api" 
spec:
  selector:
    matchLabels:
      k8s-app: "auth-api"
  replicas: 10
  template:
    metadata:
      labels:
        k8s-app: "auth-api"
    spec:
      containers:
        - name: "auth-api"
          image: peopleproductsacr.azurecr.io/auth-api
          ports:
          - containerPort: 3000
          env:
            - name: COSMOS_URL
              value: people-platform-api-prod-cosmos-url@azurekeyvault
            - name: COSMOS_KEY
              value: people-platform-api-prod-cosmos-key@azurekeyvault
            - name: DB_HOST
              value: people-api-prod-database-host@azurekeyvault
            - name: DB_USER
              value: people-api-prod-database-user@azurekeyvault
            - name: DB_PASSWORD
              value: people-api-prod-database-password@azurekeyvault
            - name: DB_NAME
              value: people-api-prod-database-dbname@azurekeyvault
            - name: DB_SCHEMA
              value: people-api-prod-database-schema@azurekeyvault
            - name: JWT_SECRET
              value: secretjwt@azurekeyvault
            - name: JWT_REFRESH_SECRET
              value: refreshsecretjwt@azurekeyvault
            - name: CORS_ORIGIN
              value: https://opr.ab-inbev.com/
            - name: GLOBAL_PREFIX
              value: /api/auth-people-platform
            - name: COSMOS_DB_NAME
              value: people-platform-api-prod-cosmos-dbname@azurekeyvault
            - name: COSMOS_CONTAINER_NAME
              value: people-platform-api-prod-cosmos-dbcontainer@azurekeyvault
            - name: REQUESTER_APP_KEY_OPR
              value: requester-app-key-opr-prod@azurekeyvault
            - name: REQUESTER_APP_KEY_REWARDS
              value: requester-app-key-rewards-prod@azurekeyvault
            - name: PEOPLE_PLATFORM_API_URL
              value: http://people-platform-api/api/people-platform
            - name: SERVICE_BUS_EMPLOYEE_ADOPTION
              value: platform-auth-api-service-bus-employee-adoption-prod@azurekeyvault
            - name: SERVICE_BUS_EMPLOYEE_ADOPTION_TOPIC
              value: "platform-adoption"        
            - name: DD_ENV
              value: 'prd'                    
          volumeMounts:
          - name: token
            mountPath: "var/run/secrets/kubernetes.io/serviceaccount/"
            readOnly: true
      imagePullSecrets:
        - name: acr-registry
      serviceAccountName: service-account-platform-auth-api-prod
      volumes:
        - name: token
          secret:
            secretName: service-account-people-produts-token-nc25x

