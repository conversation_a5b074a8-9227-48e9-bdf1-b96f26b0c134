apiVersion: spv.no/v2beta1
kind: AzureKeyVaultSecret
metadata:
  name: platform-auth-api-service-bus-employee-adoption-prod
  namespace: people-product-prod
spec:
  vault:
    name: PeopleProducts-KV-GB-PRD # 1. name of key vault
    object:
      name: platform-auth-api-service-bus-employee-adoption-prod # 2. name of the akv object
      type: secret # 3. akv object type
  output:
    secret:
      name: platform-auth-api-service-bus-employee-adoption-prod # 4. kubernetes secret name
      dataKey: platform-auth-api-service-bus-employee-adoption-prod # 5. key to store object value in kubernetes secret