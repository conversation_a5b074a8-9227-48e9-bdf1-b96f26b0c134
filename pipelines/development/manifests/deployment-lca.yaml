apiVersion: apps/v1
kind: Deployment
metadata:
  namespace: "lca-dev"
  name: "auth-api"
  labels:
    k8s-app: "auth-api" 
spec:
  selector:
    matchLabels:
      k8s-app: "auth-api"
  replicas: 1
  template:
    metadata:
     labels:
      k8s-app: "auth-api"
    spec:
      containers:
        - name: "auth-api"
          image: peopleproductsacr.azurecr.io/auth-api
          ports:
          - containerPort: 3000
          env:
            - name: DB_HOST
              value: people-api-database-host@azurekeyvault
            - name: DB_USER
              value: people-api-database-user@azurekeyvault
            - name: DB_PASSWORD
              value: people-api-database-password@azurekeyvault
            - name: DB_NAME
              value: people-api-database-dbname@azurekeyvault
            - name: DB_SCHEMA
              value: people-api-database-schema@azurekeyvault
            - name: JWT_SECRET
              value: secretjwt@azurekeyvault
            - name: JWT_REFRESH_SECRET
              value: refreshsecretjwt@azurekeyvault
            - name: JWT_EXP
              value: 1d
            - name: NODE_ENV
              value: node-env-dev@azurekeyvault
            - name: GLOBAL_PREFIX
              value: /api/auth-people-platform
            - name: REQUESTER_APP_KEY_OPR
              value: requester-app-key-opr-dev@azurekeyvault
            - name: REQUESTER_APP_KEY_REWARDS
              value: requester-app-key-rewards-dev@azurekeyvault
            - name: PEOPLE_PLATFORM_API_URL
              value: http://people-platform-api/api/people-platform/
          volumeMounts:
          - name: token
            mountPath: "var/run/secrets/kubernetes.io/serviceaccount/"
            readOnly: true
      imagePullSecrets:
        - name: acr-registry
      serviceAccountName: azdev-sa-c68140-secret
      volumes:
        - name: token
          secret:
            secretName: azdev-sa-c68140-secret