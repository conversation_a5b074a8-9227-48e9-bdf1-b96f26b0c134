apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: auth-api
  # namespace: people-product-dev
  labels:
    k8s-app: "auth-api"
  annotations:
    kubernetes.io/ingress.class: nginx
    kubernetes.io/proxy-body-size: 15m
    kubernetes.io/proxy-connect-timeout: '2400'
    kubernetes.io/proxy-read-timeout: '2400'
    kubernetes.io/proxy-send-timeouT: '2400'
    nginx.org/client-max-body-size: 15m
    nginx.org/server-snippets: gzip on;
status:
  loadBalancer:
    ingress:
      - ip: **************
spec:
  tls:
    - hosts:
        - dev.opr.ab-inbev.com
      secretName: opr-tls
  rules:
    - host: dev.opr.ab-inbev.com
      http:
        paths:
          - path: /api/auth-people-platform
            pathType: Prefix
            backend:
              service:
                name: auth-api
                port:
                  number: 80
