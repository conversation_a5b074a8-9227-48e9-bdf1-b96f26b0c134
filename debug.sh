#!/bin/bash

# Set the correct Node.js version
echo "Setting Node.js version to 22.16.0..."
source ~/.nvm/nvm.sh
nvm use 22.16.0

# Verify Node.js version
echo "Current Node.js version: $(node --version)"
if [[ "$(node --version)" != "v22.16.0" ]]; then
    echo "❌ Error: Wrong Node.js version. Expected v22.16.0, got $(node --version)"
    echo "Please run: nvm use 22.16.0"
    exit 1
fi

# Set environment variables for Auth API
export DB_HOST=peopleproductssql.database.windows.net
export DB_USER=peopleproducts_app
export DB_PASSWORD=3oK6uQ65%Z4t
export DB_NAME=peopleproductsdb
export DB_SCHEMA=dbo

# Azure AD Configuration
export AZURE_CLIENT_ID="aa52a4de-800c-499d-bb36-7cef858a1d3b"
export AZURE_CLIENT_SECRET="****************************************"
export AZURE_TENANT_ID="cef04b19-7776-4a94-b89b-375c77a8f936"

# JWT Configuration
export JWT_REFRESH_SECRET="d2UgYXJlIHNlY3VyZSBzdWNrIG1lIGF2aXYgaGFoYWhh="
export JWT_SECRET="********************************************"
export JWT_EXP="15m"
export JWT_REFRESH_EXP="7d"

# Cache Configuration
export CACHE_HOST='localhost'
export CACHE_PORT='6379'

# Server Configuration
export SERVER_PORT=4000
export GLOBAL_PREFIX=api
export CORS_ORIGIN=http://localhost:3000

# API Keys and External Services
export REQUESTER_APP_KEY_OPR='your-opr-key-here'
export REQUESTER_APP_KEY_REWARDS='your-rewards-key-here'
export PEOPLE_PLATFORM_API_URL=https://dev.northstar.ab-inbev.com/api/people-platform

# Service Bus Configuration
export SERVICE_BUS_EMPLOYEE_ADOPTION="Endpoint=sb://dev-peopleplatform.servicebus.windows.net/;SharedAccessKeyName=default;SharedAccessKey=dRjkTeqKmByIfBR4LPxGujh3faPKVMX3h+ASbCQ7eiE=;"
export SERVICE_BUS_EMPLOYEE_ADOPTION_TOPIC=proxy-audit-local
export SERVICE_BUS_PROXY_AUDIT="Endpoint=sb://dev-peopleplatform.servicebus.windows.net/;SharedAccessKeyName=default;SharedAccessKey=dRjkTeqKmByIfBR4LPxGujh3faPKVMX3h+ASbCQ7eiE=;"
export SERVICE_BUS_PROXY_AUDIT_TOPIC=proxy-audit-local

# Development Environment
export NODE_ENV=development
export DD_ENV=DEVEL
export DD_TRACE_ENABLED=false

echo ""
echo "🔧 Auth API Debug Configuration:"
echo "================================"
echo "Database: ${DB_HOST}:1433/${DB_NAME}"
echo "Server Port: ${SERVER_PORT}"
echo "Global Prefix: /${GLOBAL_PREFIX}"
echo "Debug Port: 9331"
echo "Node Environment: ${NODE_ENV}"
echo ""
echo "🚀 Starting NestJS Auth API in debug mode..."
echo "Node version: $(node --version)"
echo "Debug port: 9331"
echo ""
echo "📍 Available endpoints:"
echo "  - Health Check: http://localhost:${SERVER_PORT}/${GLOBAL_PREFIX}/health"
echo "  - Auth V2: http://localhost:${SERVER_PORT}/${GLOBAL_PREFIX}/v2/auth"
echo "  - Swagger UI: http://localhost:${SERVER_PORT}/${GLOBAL_PREFIX}/swagger-ui"
echo ""
echo "🔍 Debugging Instructions:"
echo "1. Set breakpoints in VSCode"
echo "2. Use F5 to start debugging, or"
echo "3. Use 'Attach to Process' configuration with port 9331"
echo "4. The application has a global JWT auth guard - most endpoints require authentication"
echo "5. Use the /v2/auth endpoint to authenticate and get a JWT token"
echo ""
echo "⚠️  Note: This Auth API has a global JWT guard that protects all endpoints"
echo "   except those marked with @Public(). The 403 error you're seeing is likely"
echo "   due to missing or invalid JWT authentication."
echo ""

# Start the application in debug mode
npm run start:debug
