# Auth API Debug Analysis

## 🔍 Understanding the 403 Forbidden Error

The 403 Forbidden error you're encountering when calling `http://localhost:2135/deliverables` is likely due to the **global JWT authentication guard** implemented in this Auth API.

### Key Findings:

1. **Global JWT Guard**: The application has a global `JwtAuthGuard` that protects ALL endpoints by default
2. **Public Endpoints**: Only endpoints marked with `@Public()` decorator bypass authentication
3. **Missing Endpoint**: There's no `/deliverables` endpoint in this Auth API

## 📍 Available Endpoints

### Public Endpoints (No Authentication Required):
- `GET /api/health-check` - Health check endpoint
- `POST /api/v2/auth` - Authentication endpoint (Azure AD token exchange)

### Protected Endpoints (Require JWT Token):
- `GET /api/v2/auth/refresh` - Token refresh
- `POST /api/v2/auth/proxy` - Proxy authentication (requires special permissions)

### Development Only:
- `GET /api/swagger-ui` - Swagger documentation (development mode only)

## 🚨 Root Cause Analysis

The `/deliverables` endpoint you're trying to access **does not exist** in this Auth API. This API is specifically for:
- Employee authentication via Azure AD
- JWT token generation and refresh
- Proxy authentication for system-to-system calls

## 🔧 Debugging Steps

### 1. Start the Auth API in Debug Mode
```bash
./debug.sh
```

### 2. Test Available Endpoints
```bash
# Test health check (should work)
curl http://localhost:4000/api/health-check

# Test authentication endpoint
curl -X POST http://localhost:4000/api/v2/auth \
  -H "Content-Type: application/json" \
  -d '{"azureToken": "your-azure-ad-token"}'
```

### 3. Check if /deliverables Should Exist
The `/deliverables` endpoint might belong to:
- The main application (GHQ_ABI_NORTHSTAR_KPI_CATALOG_API)
- A different microservice
- A missing controller in this Auth API

## 🔐 Authentication Flow

1. **Get Azure AD Token**: User authenticates with Azure AD
2. **Exchange Token**: POST to `/api/v2/auth` with Azure token
3. **Receive JWT**: Get back employee info + JWT tokens
4. **Use JWT**: Include JWT in Authorization header for protected endpoints

## 🛠️ VSCode Debug Configuration

Create `.vscode/launch.json`:
```json
{
  "version": "0.2.0",
  "configurations": [
    {
      "name": "Attach to Auth API",
      "type": "node",
      "request": "attach",
      "port": 9331,
      "restart": true,
      "localRoot": "${workspaceFolder}",
      "remoteRoot": "${workspaceFolder}",
      "skipFiles": ["<node_internals>/**"]
    }
  ]
}
```

## 🔍 Key Files to Debug

1. **JWT Auth Guard**: `src/@core/infra/jwt/jwt-auth.guard.ts`
   - Line 21: `canActivate()` method
   - Line 22: Check for `@Public()` decorator
   - Line 30: Authorization header extraction

2. **App Module**: `src/app/app.module.ts`
   - Line 29-31: Global guard registration

3. **Auth Controller**: `src/auth/auth-v2.controller.ts`
   - Line 19-25: Public authentication endpoint

## 🎯 Next Steps

1. **Verify Endpoint Location**: Check if `/deliverables` should be in this Auth API or another service
2. **Check Other Project**: The endpoint might be in the GHQ_ABI_NORTHSTAR_KPI_CATALOG_API
3. **Add Missing Endpoint**: If it should be here, create the controller and mark it `@Public()` if needed
4. **Test Authentication**: Use the debug setup to test the auth flow

## 🚀 Quick Test Commands

```bash
# Start debug mode
./debug.sh

# In another terminal, test endpoints:
curl -v http://localhost:4000/api/health-check
curl -v http://localhost:4000/api/deliverables  # This will fail - endpoint doesn't exist
curl -v http://localhost:4000/api/swagger-ui   # View API documentation
```

## 💡 Recommendations

1. **Use the health-check endpoint** to verify the API is running
2. **Check the Swagger UI** at `/api/swagger-ui` to see all available endpoints
3. **Look for the deliverables endpoint** in the other project (GHQ_ABI_NORTHSTAR_KPI_CATALOG_API)
4. **Set breakpoints** in the JWT guard to understand the authentication flow
