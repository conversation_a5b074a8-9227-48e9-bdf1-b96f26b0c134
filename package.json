{"name": "people-api-auth", "version": "1.0.0", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"coverage": "jest --coverage --coverageReporters=cobertura", "prebuild": "<PERSON><PERSON><PERSON> dist", "build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "NODE_ENV=development nest start --watch", "start:debug": "nest start --debug 'node --inspect=0.0.0.0:9331' --watch", "start:prod": "NODE_ENV=production node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest --runInBand", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json"}, "dependencies": {"@azure/service-bus": "^7.9.5", "@nestjs/common": "^11.0.16", "@nestjs/config": "^4.0.2", "@nestjs/core": "^11.0.16", "@nestjs/jwt": "^11.0.0", "@nestjs/passport": "^11.0.5", "@nestjs/platform-express": "^11.1.2", "@nestjs/swagger": "^11.1.2", "@nestjs/typeorm": "^11.0.0", "abi-opr-common-types": "^2.4.194", "axios": "1.11.0", "cache-manager": "^6.4.2", "cache-manager-redis-store": "^3.0.1", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "dd-trace": "^5.48.1", "jsonwebtoken": "^9.0.2", "mssql": "^11.0.1", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "reflect-metadata": "^0.2.2", "rimraf": "^6.0.1", "rxjs": "^7.8.2", "tedious": "^18.6.1", "typeorm": "^0.3.22"}, "devDependencies": {"@nestjs/cli": "^11.0.6", "@nestjs/schematics": "^11.0.5", "@nestjs/testing": "^11.0.16", "@types/express": "^5.0.1", "@types/jest": "29.5.14", "@types/node": "^22.14.1", "@types/passport-jwt": "^4.0.1", "@typescript-eslint/eslint-plugin": "^8.29.1", "@typescript-eslint/parser": "^8.29.1", "eslint": "^9.24.0", "eslint-config-prettier": "^10.1.2", "eslint-plugin-prettier": "^5.2.6", "jest": "29.7.0", "prettier": "^3.5.3", "ts-jest": "29.3.1", "ts-loader": "^9.5.2", "ts-node": "^10.9.2", "tsconfig-paths": "4.2.0", "typescript": "^5.8.3", "webpack": "^5.99.5"}, "lint-staged": {"*.ts": ["eslint --cache --fix --max-warnings=0", "jest -b --findRelatedTests --passWithNoTests"]}, "jest": {"clearMocks": true, "moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["<rootDir>/**/*.ts", "!<rootDir>/**/*.module.ts", "!<rootDir>/@core/application/dto/**/*.ts", "!<rootDir>/@core/application/mv/**/*.ts", "!<rootDir>/**/*mock.ts", "!<rootDir>/@core/infra/db/**/*.ts", "!<rootDir>/@core/infra/entities/**/*", "!<rootDir>/**/*.interceptor.ts", "!<rootDir>/**/mocks/**/*", "!<rootDir>/**/constants/**/*", "!<rootDir>/**/domain/**/*"], "coverageDirectory": "../coverage", "testEnvironment": "node", "moduleDirectories": ["node_modules", "src"], "moduleNameMapper": {"^@/(.*)$": "<rootDir>/$1", "^@Application/(.*)$": "<rootDir>/@core/application"}}}